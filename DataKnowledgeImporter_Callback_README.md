# DataKnowledgeImporter 回调机制使用说明

## 概述

`DataKnowledgeImporter` 类现在支持工具调用前的参数处理回调机制。当某个Function被调用时，可以通过回调函数获取被调用工具的 `tool.id`、被调用的参数的 `key` 和 `value`，对这些入参进行二次加工后再调用 `GetData` 方法。

## 功能特性

- **参数拦截**: 在工具调用 `GetData` 之前拦截所有参数
- **参数处理**: 对参数进行验证、转换、清理等二次加工
- **错误处理**: 回调函数执行失败时自动回退到原始参数
- **日志记录**: 详细的日志记录，便于调试和监控

## 核心组件

### 1. 回调委托定义

```csharp
/// <summary>
/// 工具调用前的参数处理回调委托
/// </summary>
/// <param name="toolId">被调用工具的ID</param>
/// <param name="parameters">被调用的参数字典(key-value)</param>
/// <returns>处理后的参数字典</returns>
public delegate Task<Dictionary<string, string>> ToolParameterProcessorCallback(string toolId, Dictionary<string, string> parameters);
```

### 2. 设置回调函数

```csharp
/// <summary>
/// 设置工具参数处理回调函数
/// </summary>
/// <param name="callback">回调函数</param>
public void SetParameterProcessorCallback(ToolParameterProcessorCallback callback)
```

## 使用方法

### 1. 基本用法

```csharp
// 创建 DataKnowledgeImporter 实例
var dataKnowledgeImporter = new DataKnowledgeImporter(kernel, serviceProvider);

// 设置参数处理回调函数
dataKnowledgeImporter.SetParameterProcessorCallback(async (toolId, parameters) =>
{
    // 对参数进行处理
    var processedParameters = new Dictionary<string, string>();
    
    foreach (var kvp in parameters)
    {
        // 示例：将所有参数值转换为大写
        processedParameters[kvp.Key] = kvp.Value?.ToUpper() ?? "";
    }
    
    return processedParameters;
});

// 正常使用 DataKnowledgeImporter
await dataKnowledgeImporter.QueryAllVectorDatabaseDataAsync(knowledgeCodes);
```

### 2. 高级用法示例

```csharp
// 设置复杂的参数处理回调
dataKnowledgeImporter.SetParameterProcessorCallback(async (toolId, parameters) =>
{
    var processedParameters = new Dictionary<string, string>();
    
    foreach (var kvp in parameters)
    {
        var key = kvp.Key;
        var value = kvp.Value;
        
        // 根据参数名进行不同处理
        switch (key.ToLower())
        {
            case "date":
            case "startdate":
            case "enddate":
                // 日期参数处理
                if (DateTime.TryParse(value, out var dateValue))
                {
                    processedParameters[key] = dateValue.ToString("yyyy-MM-dd");
                }
                else
                {
                    processedParameters[key] = DateTime.Now.ToString("yyyy-MM-dd");
                }
                break;
                
            case "amount":
            case "price":
                // 数值参数处理
                if (decimal.TryParse(value, out var numericValue))
                {
                    processedParameters[key] = numericValue.ToString("F2");
                }
                else
                {
                    processedParameters[key] = "0.00";
                }
                break;
                
            default:
                // 其他参数清理
                processedParameters[key] = value?.Trim() ?? "";
                break;
        }
    }
    
    // 根据工具ID进行特殊处理
    if (toolId.Contains("sales"))
    {
        processedParameters["department"] = "sales";
    }
    
    return processedParameters;
});
```

## 执行流程

1. **工具调用**: 当某个通过 `GenerateToolFromMetadata` 生成的工具被调用时
2. **参数收集**: 系统收集所有传入的参数 (key-value 对)
3. **回调执行**: 如果设置了回调函数，则调用回调函数处理参数
4. **参数处理**: 回调函数对参数进行验证、转换、清理等操作
5. **GetData调用**: 使用处理后的参数调用 `GetData` 方法
6. **错误处理**: 如果回调函数执行失败，使用原始参数继续执行

## 日志记录

系统会记录详细的日志信息：

```
[GetDataFunc] 调用参数处理回调函数，工具ID: {toolId}, 原始参数: {parameters}
[GetDataFunc] 参数处理回调函数执行完成，处理后参数: {parameters}
[GetDataFunc] 参数处理回调函数执行失败，工具ID: {toolId}
```

## 错误处理

- 如果回调函数执行过程中发生异常，系统会记录错误日志
- 发生异常时，系统会自动使用原始参数继续执行，确保工具调用不会失败
- 这种设计保证了系统的健壮性

## 注意事项

1. **异步处理**: 回调函数是异步的，可以执行异步操作
2. **参数类型**: 所有参数都是字符串类型，需要在回调中进行类型转换
3. **性能考虑**: 回调函数会在每次工具调用时执行，应避免耗时操作
4. **线程安全**: 回调函数可能在多线程环境中执行，需要确保线程安全
5. **参数验证**: 建议在回调函数中进行参数验证，防止无效数据传递

## 完整示例

参考 `DataKnowledgeImporterUsageExample.cs` 文件，其中包含了完整的使用示例，展示了：

- 日期参数处理
- 数值参数处理
- 用户ID验证
- 根据工具ID进行特殊处理
- 错误处理和日志记录

这个回调机制为 `DataKnowledgeImporter` 提供了强大的参数处理能力，可以满足各种复杂的业务需求。
